.sppb-addon-holiday-tabs {
    position: relative;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs {
    display: flex;
    overflow-x: auto;
    overflow-y: visible;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch;
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    border-bottom: none;
    background: transparent;
    position: relative;
    z-index: 2;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.sppb-addon-holiday-tabs .sppb-nav-tabs::-webkit-scrollbar {
    display: none;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs .sppb-tab {
    flex: 0 0 auto;
    white-space: nowrap;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs > div {
    margin-bottom: -1px;
    padding: 15px 25px;
    cursor: pointer;
    color: rgba(255,255,255,0.8);
    border: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    border-radius: 10px 10px 0 0;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs > div:hover {
    color: #fff;
    background-color: rgba(255,255,255,0.1);
}

.sppb-addon-holiday-tabs .sppb-nav-tabs > div.active {
    color: #000;
    font-weight: 600;
    background: #fff;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs > div.active:after {
    display: none;
}

.sppb-addon-holiday-tabs .sppb-nav-tabs > div.active .sppb-tab-icon svg {
    color: #000;
    fill: currentColor;
}

.sppb-addon-holiday-tabs .sppb-tab-content,
.sppb-addon-holiday-tabs .sppb-addon-content .sppb-tab-content {
    margin-top: 0;
    padding: 50px 0 30px;
    background-color: #E3E8EE;
    position: relative;
    min-height: 400px;
    overflow: visible;
}

.sppb-addon-holiday-tabs .sppb-tab-pane {
    position: absolute;
    left: 0;
    right: 0;
    padding-left: 15px;
    padding-right: 0;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.1s ease, visibility 0.1s ease;
    will-change: opacity, visibility;
    pointer-events: none;
    overflow: visible;
}

.sppb-addon-holiday-tabs .sppb-tab-pane.active {
    position: relative;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    overflow: visible;
}

/* Category Header */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 0 15px;
    gap: 2rem;
}

.category-header-content {
    min-width: 0;
}

.category-header-action {
    width: fit-content;
    white-space: nowrap;
}

.category-header-action .btn-primary {
    background: #e74c3c;
    border: none;
    padding: 12px 25px;
    border-radius: 4px;
    color: #fff;
    text-decoration: none;
    transition: background 0.3s ease;
    white-space: nowrap;
}

.category-header-action .btn-primary:hover {
    background: #d44333;
}

.category-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin: 0 0 15px;
    color: #333;
}

.category-description {
    font-size: 1.1rem;
    color: #666;
}

/* Holiday Grid - Slick Slider Implementation */
.holiday-grid-container {
    margin: 30px auto;
    max-width: 1400px;
    position: relative;
    overflow: visible;
    padding-top: 20px;
    padding-left: 75px;
    padding-right: 75px;
}

.holiday-grid {
    margin: 30px 0;
    position: relative;
    overflow: visible;
}

/* Slick slider overrides for holiday tabs */
.holiday-grid.slick-slider {
    margin: 30px 0;
    overflow: visible;
}

.holiday-grid.slick-slider .slick-list {
    margin: 0;
    padding: 0;
    overflow: visible;
}

.holiday-grid.slick-slider .slick-track {
    display: flex;
    align-items: stretch;
    overflow: visible;
}

.holiday-grid.slick-slider .slick-slide {
    padding: 0 15px;
    height: auto;
}

.holiday-grid.slick-slider .slick-slide > div {
    height: 100%;
}

/* Arrow styling based on reference page */
.holiday-grid.slick-slider .slick-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex !important;
    align-items: center;
    justify-content: center;
    font-size: 0;
}

.holiday-grid.slick-slider .slick-arrow:hover {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.holiday-grid.slick-slider .slick-arrow:before {
    content: '';
    display: block;
    width: 12px;
    height: 12px;
    border-top: 2px solid #333;
    border-right: 2px solid #333;
}

.holiday-grid.slick-slider .slick-prev {
    left: -60px;
}

.holiday-grid.slick-slider .slick-prev:before {
    transform: rotate(-135deg);
    margin-left: 3px;
}

.holiday-grid.slick-slider .slick-next {
    right: -60px;
}

.holiday-grid.slick-slider .slick-next:before {
    transform: rotate(45deg);
    margin-right: 3px;
}

/* Hide arrows on mobile */
@media (max-width: 768px) {
    .holiday-grid.slick-slider .slick-arrow {
        display: none !important;
    }
}

/* Dots styling */
.holiday-grid.slick-slider .slick-dots {
    bottom: -50px;
    text-align: center;
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex !important;
    justify-content: center;
    gap: 8px;
}

.holiday-grid.slick-slider .slick-dots li {
    width: 12px;
    height: 12px;
    margin: 0;
}

.holiday-grid.slick-slider .slick-dots li button {
    width: 12px;
    height: 12px;
    padding: 0;
    border: none;
    border-radius: 50%;
    background: #ccc;
    cursor: pointer;
    font-size: 0;
    transition: background 0.3s ease;
}

.holiday-grid.slick-slider .slick-dots li button:hover {
    background: #999;
}

.holiday-grid.slick-slider .slick-dots li.slick-active button {
    background: #e74c3c;
}

/* Show dots on all screen sizes */
@media (min-width: 769px) {
    .holiday-grid.slick-slider .slick-dots {
        display: flex !important;
    }
}

/* Fallback for non-slider (mobile) */
.holiday-grid:not(.slick-slider) {
    display: flex;
    gap: 30px;
    padding: 0 15px;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    align-items: stretch;
}

.holiday-grid:not(.slick-slider)::-webkit-scrollbar {
    display: none;
}

/* Large screens - remove container padding to use full width and reposition arrows */
@media (min-width: 1401px) {
    .holiday-grid-container {
        padding-left: 0;
        padding-right: 0;
    }

    /* Position arrows outside the container at large screens */
    .holiday-grid.slick-slider .slick-prev {
        left: -75px;
    }

    .holiday-grid.slick-slider .slick-next {
        right: -75px;
    }
}

@media (max-width: 1400px) {
    .holiday-grid:not(.slick-slider) {
        padding-right: 0;
    }

    .sppb-addon-holiday-tabs .sppb-tab-pane {
        padding-right: 0;
    }
}

@media (max-width: 1259px) {
    .holiday-grid-container {
        margin: 30px -15px;
        padding-left: 60px;
        padding-right: 60px;
    }
}

@media (max-width: 1024px) {
    .holiday-grid-container {
        padding-left: 45px;
        padding-right: 45px;
    }

    .holiday-grid.slick-slider .slick-prev {
        left: -35px;
    }

    .holiday-grid.slick-slider .slick-next {
        right: -35px;
    }
}

@media (max-width: 768px) {
    /* Tablet-specific slider adjustments (576-768px) */
    .holiday-grid.slick-slider .slick-slide {
        padding: 0 10px;
    }
}

@media (max-width: 576px) {
    /* Hide category header content on mobile */
    .category-header-content {
        display: none;
    }

    /* Restructure category header for mobile */
    .category-header {
        flex-direction: column;
        margin-bottom: 20px;
        padding: 0;
        order: 2;
        margin-left: -15px;
        margin-right: -15px;
        width: auto;
    }

    .category-header-action {
        width: 100%;
        padding: 0 15px;
        margin-right: 15px;
    }

    /* Make the tab pane content use flexbox for ordering */
    .sppb-tab-pane.active {
        display: flex;
        flex-direction: column;
    }

    /* Adjust holiday grid for horizontal scrolling and remove arrow padding */
    .holiday-grid-container {
        order: 1;
        margin: 0 -15px;
        padding-bottom: 20px;
        padding-left: 15px;
        padding-right: 15px;
    }

    .holiday-grid {
        padding: 0 15px;
    }

    /* Ensure the grid container spans full width */
    .sppb-tab-pane {
        padding-right: 0 !important;
        padding-left: 0 !important;
    }

    /* Adjust button styling for mobile */
    .category-header-action .btn-primary {
        width: 100%;
        text-align: center;
        justify-content: center;
    }
}

/* Holiday Cards */
.holiday-grid .zen-card {
    width: 100%;
    height: 100%;
}

.zen-card {
    height: 100%;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.zen-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.zen-card__body {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.zen-card__image {
    position: relative;
    margin: -20px -20px 20px;
    overflow: hidden;
    border-radius: 4px 4px 0 0;
}

.zen-card__image img {
    width: 100%;
    height: 275px;
    object-fit: cover;
    display: block;
}

.zen-card__image--max-height {
    max-height: 275px;
}

.zen-card__image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.6);
    padding: 10px;
    color: #fff;
}

.zen-card__info {
    margin: 15px 0;
    padding: 15px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.zen-flex-end {
    margin-top: auto;
}

.zen-pill {
    display: inline-block;
    padding: 5px 10px;
    background: #f5f5f5;
    border-radius: 20px;
    font-size: 0.9em;
}

.disabled {
    text-decoration: line-through;
    opacity: 0.7;
}

.grade-image {
    height: 24px;
    width: auto;
}

.sppb-addon-holiday-tabs .sppb-tab-icon {
    display: inline-block;
    margin-right: 8px;
    vertical-align: middle;
}

.sppb-addon-holiday-tabs .sppb-tab-icon img.svg-icon {
    width: 24px;
    height: 24px;
    vertical-align: middle;
}

.sppb-addon-holiday-tabs .sppb-tab-icon svg {
    width: 24px;
    height: 24px;
    fill: currentColor;
    color: inherit;
}

.sppb-tab.active .sppb-tab-icon svg {
    color: inherit;
    fill: currentColor;
}

.sppb-addon-holiday-tabs .sppb-tab-title {
    vertical-align: middle;
}

/* Add transitions for height equalization */
.zen-card__image,
.zen-title,
.zen-text--subtitle,
.zen-card__info,
.zen-text--text-df,
.zen-price {
    display: block;
    min-height: 0;
    height: auto;
    transition: height 0.5s ease;
    will-change: height;
}

/* Equalizer Grid Styles */
.equalize-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    padding: 0 15px;
    margin: 0;
    width: 100%;
}

.equalize-grid .zen-card {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.equalize-grid .zen-card__image,
.equalize-grid .zen-title,
.equalize-grid .zen-text--subtitle,
.equalize-grid .zen-card__info,
.equalize-grid .zen-text--text-df,
.equalize-grid .zen-price {
    height: 100%;
}

/* Mobile layout */
.mobile-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.mobile-layout .zen-card {
    height: auto;
}

.mobile-layout .zen-card__image,
.mobile-layout .zen-title,
.mobile-layout .zen-text--subtitle,
.mobile-layout .zen-card__info,
.mobile-layout .zen-text--text-df,
.mobile-layout .zen-price {
    height: auto !important;
} 

.zen-card__body .difficulty-info-btn {color: rgb(38, 38, 38)!important; }

/* Difficulty Info Button Styles */
.difficulty-info-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0;
}

.difficulty-info-btn:hover {
    opacity: 0.8;
}

.difficulty-icon {
    display: flex;
    align-items: center;
}

.difficulty-text {
    margin: 0 4px;
}